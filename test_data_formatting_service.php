<?php

require_once __DIR__ . '/vendor/autoload.php';

use TF\Engine\Plugins\Core\Payments\Payments_Laravel\Services\DataFormattingService;

// Simple test to verify the refactored DataFormattingService works correctly
$service = new DataFormattingService();

// Test data
$testData = [
    [
        'owner_id' => 1,
        'owner_names' => '<PERSON>',
        'contract_id' => 'C001',
        'c_num' => '001',
        'all_owner_area' => 123.456789,
        'owner_area' => 100.123,
        'pu_area' => 23.333789,
        'cultivated_area' => 99.999,
        'renta' => 1500.50,
        'charged_renta' => 1500.50,
        'paid_renta' => 1000.25,
        'unpaid_renta' => 500.25,
        'overpaid_renta' => 0,
        'paid_via_money' => 1000.25,
        'paid_via_nat' => 0,
        'renta_nat' => [],
        'unpaid_renta_nat_arr' => [],
        'overpaid_renta_nat_arr' => [],
        'paid_renta_nat_sum' => [],
        'is_dead' => false,
        'allow_owner_payment' => true,
        'is_heritor' => false,
        'contract_start_date' => '2023-01-01',
        'contract_due_date' => '2024-12-31',
        'farming_name' => 'Test Farm',
    ]
];

try {
    echo "Testing DataFormattingService with Laravel Number helper...\n\n";
    
    // Test formatOwnersData
    $formattedData = $service->formatOwnersData($testData);
    
    echo "Formatted Owner Data:\n";
    echo "Area formatting (3 decimals):\n";
    echo "- all_owner_area: " . $formattedData[0]['all_owner_area'] . "\n";
    echo "- owner_area: " . $formattedData[0]['owner_area'] . "\n";
    echo "- pu_area: " . $formattedData[0]['pu_area'] . "\n";
    echo "- cultivated_area: " . $formattedData[0]['cultivated_area'] . "\n";
    
    echo "\nMoney formatting (2 decimals):\n";
    echo "- renta: " . $formattedData[0]['renta'] . "\n";
    echo "- charged_renta: " . $formattedData[0]['charged_renta'] . "\n";
    echo "- paid_renta: " . $formattedData[0]['paid_renta'] . "\n";
    echo "- unpaid_renta: " . $formattedData[0]['unpaid_renta'] . "\n";
    
    // Test generateFooter
    $footer = $service->generateFooter($testData);
    
    echo "\nFooter Data:\n";
    echo "- all_owner_area: " . $footer['all_owner_area'] . "\n";
    echo "- renta: " . $footer['renta'] . "\n";
    
    echo "\n✅ All tests passed! Laravel Number helper is working correctly.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
