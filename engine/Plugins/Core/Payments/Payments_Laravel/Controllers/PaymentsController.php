<?php

namespace TF\Engine\Plugins\Core\Payments\Payments_Laravel\Controllers;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use TF\Engine\Plugins\Core\Payments\Payments_Laravel\Services\PaymentsService;
use TF\Engine\Plugins\Core\Payments\Payments_Laravel\Services\OwnerPaymentsService;

/**
 * Laravel-style Payments Controller
 * 
 * Converted from the original PaymentsController.php to use Laravel conventions
 * and Query Builder patterns while maintaining all business logic.
 */
class PaymentsController
{
    protected PaymentsService $paymentsService;
    protected OwnerPaymentsService $ownerPaymentsService;

    public function __construct(
        PaymentsService $paymentsService,
        OwnerPaymentsService $ownerPaymentsService
    ) {
        $this->paymentsService = $paymentsService;
        $this->ownerPaymentsService = $ownerPaymentsService;
    }

    /**
     * Get owner payments for a specific year and owner
     * 
     * This is the main method that was analyzed - converts the original getOwnerPayments() method
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getOwnerPayments(Request $request): JsonResponse
    {
        try {
            $year = $request->input('year');
            $ownerId = $request->input('owner_id');

            if (empty($ownerId)) {
                return response()->json([
                    'rows' => [],
                    'total' => 0,
                    'footer' => [],
                ]);
            }

            $result = $this->ownerPaymentsService->getOwnerPayments($year, $ownerId);

            return response()->json($result);

        } catch (Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'rows' => [],
                'total' => 0,
                'footer' => [],
            ], 500);
        }
    }

    /**
     * Get owners payroll data
     * 
     * Converts the original getOwnersPayroll() method
     */
    public function getOwnersPayroll(Request $request): JsonResponse
    {
        try {
            $year = $request->input('year');
            $filterParams = $request->input('filter_params', []);
            $rows = $request->input('rows');
            $page = $request->input('page');
            $returnFlatOwners = $request->input('return_flat_owners', false);

            $result = $this->paymentsService->getOwnersPayroll(
                $year, 
                $filterParams, 
                $rows, 
                $page, 
                $returnFlatOwners
            );

            return response()->json($result);

        } catch (Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'rows' => [],
                'total' => 0,
                'footer' => [],
            ], 500);
        }
    }

    /**
     * Get owner payroll for a specific owner
     * 
     * Converts the original getOwnerPayroll() method
     */
    public function getOwnerPayroll(Request $request): JsonResponse
    {
        try {
            $year = $request->input('year');
            $ownerId = $request->input('owner_id');
            $path = $request->input('path');
            $filterParams = $request->input('filter_params', []);

            $result = $this->paymentsService->getOwnerPayroll($year, $ownerId, $path, $filterParams);

            return response()->json($result);

        } catch (Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'rows' => [],
            ], 500);
        }
    }

    /**
     * Get contract payments
     * 
     * Converts the original getContractPayments() method
     */
    public function getContractPayments(Request $request): JsonResponse
    {
        try {
            $year = $request->input('year');
            $contractId = $request->input('contract_id');
            $annexId = $request->input('annex_id');
            $page = $request->input('page');
            $rows = $request->input('rows');
            $sort = $request->input('sort');
            $order = $request->input('order');

            $result = $this->paymentsService->getContractPayments(
                $year, 
                $contractId, 
                $annexId, 
                $page, 
                $rows, 
                $sort, 
                $order
            );

            return response()->json($result);

        } catch (Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'rows' => [],
                'total' => 0,
                'footer' => [],
            ], 500);
        }
    }
}
