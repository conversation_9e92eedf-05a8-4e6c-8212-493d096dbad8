<?php

namespace TF\Engine\Plugins\Core\Payments\Payments_Laravel\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * Owner Model
 * 
 * Represents an owner in the system using Laravel Eloquent ORM patterns.
 */
class Owner extends Model
{
    protected $table = 'su_owners';
    
    protected $fillable = [
        'name',
        'surname', 
        'lastname',
        'company_name',
        'egn',
        'eik',
        'owner_type',
        'address',
        'company_address',
        'phone',
        'mobile',
        'email',
        'iban',
        'lk_nomer',
        'lk_izdavane',
        'rent_place',
        'is_dead',
        'dead_date',
        'post_payment_fields',
    ];

    protected $casts = [
        'is_dead' => 'boolean',
        'dead_date' => 'date',
        'post_payment_fields' => 'array',
        'owner_type' => 'integer',
    ];

    /**
     * Get the owner's full name
     */
    public function getFullNameAttribute(): string
    {
        if ($this->owner_type == 1) {
            return trim($this->name . ' ' . $this->surname . ' ' . $this->lastname);
        }
        
        return $this->company_name ?? '';
    }

    /**
     * Get the owner's identification number (EGN or EIK)
     */
    public function getIdentificationAttribute(): string
    {
        return $this->owner_type == 1 ? $this->egn : $this->eik;
    }

    /**
     * Get the owner's address
     */
    public function getAddressAttribute(): string
    {
        return $this->owner_type == 1 ? $this->address : $this->company_address;
    }

    /**
     * Check if owner allows payments based on death status and farm year
     */
    public function allowsPayment(string $farmYearStart, string $farmYearEnd): bool
    {
        if (!$this->is_dead) {
            return true;
        }

        if ($this->is_dead && !$this->dead_date) {
            return false;
        }

        if ($this->is_dead && $this->dead_date >= $farmYearStart && $this->dead_date <= $farmYearEnd) {
            return true;
        }

        if ($this->is_dead && $this->dead_date > $farmYearStart) {
            return true;
        }

        return false;
    }

    /**
     * Check if death date is in current farm year
     */
    public function isDeadDateInCurrentFarmYear(string $farmYearStart, string $farmYearEnd): bool
    {
        if (!$this->is_dead) {
            return true;
        }

        return $this->dead_date >= $farmYearStart && $this->dead_date <= $farmYearEnd;
    }

    /**
     * Relationship: Owner has many payments
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'owner_id');
    }

    /**
     * Relationship: Owner has many plot relationships
     */
    public function plotRelationships(): HasMany
    {
        return $this->hasMany(PlotOwnerRelation::class, 'owner_id');
    }

    /**
     * Relationship: Owner belongs to many contracts through plots
     */
    public function contracts(): BelongsToMany
    {
        return $this->belongsToMany(Contract::class, 'su_plots_owners_rel', 'owner_id', 'contract_id')
                    ->through('su_contracts_plots_rel');
    }

    /**
     * Relationship: Owner has many personal use records
     */
    public function personalUse(): HasMany
    {
        return $this->hasMany(PersonalUse::class, 'owner_id');
    }

    /**
     * Scope: Filter by owner type (individual or company)
     */
    public function scopeByType($query, int $type)
    {
        return $query->where('owner_type', $type);
    }

    /**
     * Scope: Filter by alive owners
     */
    public function scopeAlive($query)
    {
        return $query->where('is_dead', false);
    }

    /**
     * Scope: Filter by dead owners
     */
    public function scopeDead($query)
    {
        return $query->where('is_dead', true);
    }

    /**
     * Scope: Filter by EGN
     */
    public function scopeByEgn($query, string $egn)
    {
        return $query->where('egn', $egn);
    }

    /**
     * Scope: Filter by EIK
     */
    public function scopeByEik($query, string $eik)
    {
        return $query->where('eik', $eik);
    }

    /**
     * Scope: Search by name
     */
    public function scopeSearchByName($query, string $name)
    {
        return $query->where(function ($q) use ($name) {
            $q->whereRaw("CONCAT(name, ' ', surname, ' ', lastname) ILIKE ?", ["%{$name}%"])
              ->orWhere('company_name', 'ILIKE', "%{$name}%");
        });
    }
}
