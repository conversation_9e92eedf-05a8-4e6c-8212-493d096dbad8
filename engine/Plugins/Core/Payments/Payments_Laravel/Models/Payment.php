<?php

namespace TF\Engine\Plugins\Core\Payments\Payments_Laravel\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Payment Model
 * 
 * Represents a payment transaction in the system using Laravel Eloquent ORM patterns.
 */
class Payment extends Model
{
    protected $table = 'su_payments';
    
    protected $fillable = [
        'contract_id',
        'owner_id',
        'trans_amount',
        'trans_amount_nat',
        'trans_date',
        'paid_in',
        'paid_from',
        'nat_type',
        'unit_value',
        'payment_type',
        'payment_method',
        'description',
        'created_by',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'trans_amount' => 'decimal:2',
        'trans_amount_nat' => 'decimal:3',
        'trans_date' => 'date',
        'unit_value' => 'decimal:2',
        'paid_in' => 'integer',
        'paid_from' => 'integer',
        'nat_type' => 'integer',
        'payment_type' => 'integer',
        'payment_method' => 'integer',
        'contract_id' => 'integer',
        'owner_id' => 'integer',
        'created_by' => 'integer',
    ];

    /**
     * Payment types constants
     */
    const PAYMENT_TYPE_RENT = 1;
    const PAYMENT_TYPE_NATURAL_RENT = 2;
    const PAYMENT_TYPE_PERSONAL_USE = 3;

    /**
     * Payment methods constants
     */
    const PAYMENT_METHOD_CASH = 1;
    const PAYMENT_METHOD_BANK_TRANSFER = 2;
    const PAYMENT_METHOD_NATURAL = 3;

    /**
     * Paid in constants
     */
    const PAID_IN_MONEY = 1;
    const PAID_IN_NATURAL = 2;

    /**
     * Paid from constants
     */
    const PAID_FROM_RENT = 1;
    const PAID_FROM_NATURAL_RENT = 2;

    /**
     * Check if payment is in money
     */
    public function getIsMoneyPaymentAttribute(): bool
    {
        return $this->paid_in === self::PAID_IN_MONEY;
    }

    /**
     * Check if payment is in natural form
     */
    public function getIsNaturalPaymentAttribute(): bool
    {
        return $this->paid_in === self::PAID_IN_NATURAL;
    }

    /**
     * Check if payment is for rent
     */
    public function getIsRentPaymentAttribute(): bool
    {
        return $this->paid_from === self::PAID_FROM_RENT;
    }

    /**
     * Check if payment is for natural rent
     */
    public function getIsNaturalRentPaymentAttribute(): bool
    {
        return $this->paid_from === self::PAID_FROM_NATURAL_RENT;
    }

    /**
     * Get payment amount in money equivalent
     */
    public function getMoneyEquivalentAttribute(): float
    {
        if ($this->is_money_payment) {
            return $this->trans_amount;
        }
        
        return $this->trans_amount_nat * $this->unit_value;
    }

    /**
     * Get formatted payment description
     */
    public function getFormattedDescriptionAttribute(): string
    {
        $description = $this->description ?? '';
        
        if ($this->is_natural_payment && $this->naturalRentType) {
            $description .= ' (' . $this->naturalRentType->name . ')';
        }
        
        return $description;
    }

    /**
     * Relationship: Payment belongs to contract
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    /**
     * Relationship: Payment belongs to owner
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(Owner::class, 'owner_id');
    }

    /**
     * Relationship: Payment belongs to natural rent type
     */
    public function naturalRentType(): BelongsTo
    {
        return $this->belongsTo(NaturalRentType::class, 'nat_type');
    }

    /**
     * Relationship: Payment belongs to creator (user)
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope: Filter by contract
     */
    public function scopeByContract($query, $contractId)
    {
        return $query->where('contract_id', $contractId);
    }

    /**
     * Scope: Filter by owner
     */
    public function scopeByOwner($query, $ownerId)
    {
        return $query->where('owner_id', $ownerId);
    }

    /**
     * Scope: Filter by year
     */
    public function scopeByYear($query, int $year)
    {
        $farmYearStart = ($year - 1) . '-10-01';
        $farmYearEnd = $year . '-09-30';
        
        return $query->whereBetween('trans_date', [$farmYearStart, $farmYearEnd]);
    }

    /**
     * Scope: Filter money payments
     */
    public function scopeMoneyPayments($query)
    {
        return $query->where('paid_in', self::PAID_IN_MONEY);
    }

    /**
     * Scope: Filter natural payments
     */
    public function scopeNaturalPayments($query)
    {
        return $query->where('paid_in', self::PAID_IN_NATURAL);
    }

    /**
     * Scope: Filter rent payments
     */
    public function scopeRentPayments($query)
    {
        return $query->where('paid_from', self::PAID_FROM_RENT);
    }

    /**
     * Scope: Filter natural rent payments
     */
    public function scopeNaturalRentPayments($query)
    {
        return $query->where('paid_from', self::PAID_FROM_NATURAL_RENT);
    }

    /**
     * Scope: Filter by payment type
     */
    public function scopeByType($query, int $type)
    {
        return $query->where('payment_type', $type);
    }

    /**
     * Scope: Filter by payment method
     */
    public function scopeByMethod($query, int $method)
    {
        return $query->where('payment_method', $method);
    }

    /**
     * Scope: Filter by date range
     */
    public function scopeByDateRange($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('trans_date', [$startDate, $endDate]);
    }
}
