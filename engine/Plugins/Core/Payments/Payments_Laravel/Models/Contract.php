<?php

namespace TF\Engine\Plugins\Core\Payments\Payments_Laravel\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * Contract Model
 * 
 * Represents a contract in the system using Laravel Eloquent ORM patterns.
 */
class Contract extends Model
{
    protected $table = 'su_contracts';
    
    protected $fillable = [
        'c_num',
        'parent_id',
        'start_date',
        'due_date',
        'farming_id',
        'rent_money_value',
        'rent_place',
        'contract_type',
        'status',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'start_date' => 'date',
        'due_date' => 'date',
        'rent_money_value' => 'decimal:2',
        'farming_id' => 'integer',
        'parent_id' => 'integer',
        'contract_type' => 'integer',
        'status' => 'integer',
    ];

    /**
     * Check if this is an annex (has parent contract)
     */
    public function getIsAnnexAttribute(): bool
    {
        return !is_null($this->parent_id);
    }

    /**
     * Get the contract number with parent info if annex
     */
    public function getFullContractNumberAttribute(): string
    {
        if ($this->is_annex && $this->parent) {
            return $this->parent->c_num . ' / ' . $this->c_num;
        }
        
        return $this->c_num;
    }

    /**
     * Check if contract is active for given year
     */
    public function isActiveForYear(int $year): bool
    {
        $farmYearStart = ($year - 1) . '-10-01';
        $farmYearEnd = $year . '-09-30';
        
        return $this->start_date <= $farmYearEnd && 
               ($this->due_date >= $farmYearStart || is_null($this->due_date));
    }

    /**
     * Relationship: Contract belongs to parent contract (for annexes)
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'parent_id');
    }

    /**
     * Relationship: Contract has many annexes
     */
    public function annexes(): HasMany
    {
        return $this->hasMany(Contract::class, 'parent_id');
    }

    /**
     * Relationship: Contract belongs to farming
     */
    public function farming(): BelongsTo
    {
        return $this->belongsTo(Farming::class, 'farming_id');
    }

    /**
     * Relationship: Contract has many plot relationships
     */
    public function plotRelationships(): HasMany
    {
        return $this->hasMany(ContractPlotRelation::class, 'contract_id');
    }

    /**
     * Relationship: Contract has many payments
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'contract_id');
    }

    /**
     * Relationship: Contract belongs to many owners through plots
     */
    public function owners(): BelongsToMany
    {
        return $this->belongsToMany(Owner::class, 'su_plots_owners_rel', 'contract_id', 'owner_id')
                    ->through('su_contracts_plots_rel');
    }

    /**
     * Relationship: Contract has many natural rents
     */
    public function naturalRents(): HasMany
    {
        return $this->hasMany(ContractNaturalRent::class, 'contract_id');
    }

    /**
     * Scope: Filter by farming
     */
    public function scopeByFarming($query, $farmingId)
    {
        return $query->where('farming_id', $farmingId);
    }

    /**
     * Scope: Filter by year
     */
    public function scopeByYear($query, int $year)
    {
        $farmYearStart = ($year - 1) . '-10-01';
        $farmYearEnd = $year . '-09-30';
        
        return $query->where('start_date', '<=', $farmYearEnd)
                     ->where(function ($q) use ($farmYearStart) {
                         $q->where('due_date', '>=', $farmYearStart)
                           ->orWhereNull('due_date');
                     });
    }

    /**
     * Scope: Filter main contracts (not annexes)
     */
    public function scopeMainContracts($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope: Filter annexes only
     */
    public function scopeAnnexes($query)
    {
        return $query->whereNotNull('parent_id');
    }

    /**
     * Scope: Filter by contract number
     */
    public function scopeByNumber($query, string $number)
    {
        return $query->where('c_num', 'ILIKE', "%{$number}%");
    }

    /**
     * Scope: Filter active contracts
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Get total contract area
     */
    public function getTotalAreaAttribute(): float
    {
        return $this->plotRelationships()
                    ->where('annex_action', 'added')
                    ->sum('area') ?? 0;
    }

    /**
     * Get total rent amount for contract
     */
    public function getTotalRentAttribute(): float
    {
        return $this->rent_money_value * $this->total_area;
    }

    /**
     * Get contract duration in days
     */
    public function getDurationAttribute(): int
    {
        if (!$this->due_date) {
            return 0;
        }
        
        return $this->start_date->diffInDays($this->due_date);
    }
}
