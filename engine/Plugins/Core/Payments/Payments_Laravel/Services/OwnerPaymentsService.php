<?php

namespace TF\Engine\Plugins\Core\Payments\Payments_Laravel\Services;

use Exception;
use Illuminate\Support\Collection;
use TF\Engine\Plugins\Core\Payments\Payments_Laravel\Repositories\PaymentsRepository;
use TF\Engine\Plugins\Core\Payments\Payments_Laravel\Services\FarmingPermissionsService;
use TF\Engine\Plugins\Core\Payments\Payments_Laravel\Services\PaymentCalculationService;
use TF\Engine\Plugins\Core\Payments\Payments_Laravel\Services\DataFormattingService;

/**
 * Owner Payments Service
 * 
 * Handles the main business logic for owner payments processing.
 * This service converts the core getOwnerPayments() method logic to Laravel patterns.
 */
class OwnerPaymentsService
{
    protected PaymentsRepository $paymentsRepository;
    protected FarmingPermissionsService $farmingPermissionsService;
    protected PaymentCalculationService $calculationService;
    protected DataFormattingService $formattingService;

    public function __construct(
        PaymentsRepository $paymentsRepository,
        FarmingPermissionsService $farmingPermissionsService,
        PaymentCalculationService $calculationService,
        DataFormattingService $formattingService
    ) {
        $this->paymentsRepository = $paymentsRepository;
        $this->farmingPermissionsService = $farmingPermissionsService;
        $this->calculationService = $calculationService;
        $this->formattingService = $formattingService;
    }

    /**
     * Get owner payments - main method that replicates the original getOwnerPayments() logic
     * 
     * @param int $year
     * @param int $ownerId
     * @return array
     * @throws Exception
     */
    public function getOwnerPayments(int $year, int $ownerId): array
    {
        if (empty($ownerId)) {
            return [
                'rows' => [],
                'total' => 0,
                'footer' => [],
            ];
        }

        $ownersPayments = [];

        // Step 1: Get allowed farming IDs (Query 1 from our analysis)
        $allowedFarmingIds = $this->farmingPermissionsService->getAllowedFarmingIds();

        // Handle case when no allowed farming IDs are available
        if (empty($allowedFarmingIds)) {
            throw new Exception('User has no allowed farmings');
        }

        // Step 2: Get all contracts for the owner (Query 2 from our analysis)
        $ownerContracts = $this->paymentsRepository->getOwnerContracts($year, [
            'owner_id' => $ownerId,
            'payroll_farming' => $allowedFarmingIds
        ]);

        // Step 3: Process each contract to get payment data
        foreach ($ownerContracts as $contract) {
            $contractId = $contract['contract_id'];
            $annexId = null;

            if ($contract['parent_id']) {
                $contractId = $contract['parent_id'];
                $annexId = $contract['contract_id'];
            }

            // Get payments for this contract (this triggers Queries 3-8 from our analysis)
            $contractsPayments = $this->getPayments($year, $contractId, $annexId);

            // Extract data for the specific owner from the contract tree
            if ($contractsPayments) {
                $ownersPayments[] = $this->calculationService->sumOwnerDataInContract(
                    $contractsPayments, 
                    $ownerId
                );
            }
        }

        // Step 4: Format and return the results
        return [
            'rows' => $this->formattingService->formatOwnersData($ownersPayments),
            'total' => count($ownersPayments),
            'footer' => [$this->formattingService->generateFooter($ownersPayments, ['timespan'])],
        ];
    }

    /**
     * Get payments for a specific contract
     * 
     * This method replicates the original getPayments() method logic
     * and orchestrates the execution of multiple database queries.
     * 
     * @param int $year
     * @param int|null $contractId
     * @param int|null $annexId
     * @param string|null $gridType
     * @param array $filterParams
     * @return array|null
     */
    public function getPayments(
        int $year, 
        ?int $contractId = null, 
        ?int $annexId = null, 
        ?string $gridType = null, 
        array $filterParams = []
    ): ?array {
        // Step 1: Get all plots for the contract with rents (Query 3 from our analysis)
        $plots = $this->getPaymentsPlots($year, $contractId, $annexId, null, null, $filterParams);

        // Step 2: Aggregate data by owner
        $aggPlots = $this->calculationService->aggregatePlots($plots);

        // Step 3: Process owner personal use
        $aggPlots = $this->calculationService->processOwnerPersonalUse($aggPlots, $gridType);

        // Step 4: Process owner payments (Queries 6-7 from our analysis)
        $contractAnnexId = [$contractId];
        if (!empty($annexId)) {
            $contractAnnexId[] = $annexId;
        }
        $aggPlots = $this->calculationService->processOwnerPayments($year, $aggPlots, $contractAnnexId);

        // Step 5: Fix rounding errors
        $aggPlots = $this->calculationService->fixRounding($aggPlots);

        // Step 6: Filter children based on parent's dead status
        $aggPlots = $this->calculationService->filterChildrenByParentDeadStatus($aggPlots);

        // Step 7: Build owners tree
        $ownersTree = $this->calculationService->buildOwnersTree($aggPlots);

        // Step 8: Calculate tree rents
        $this->calculationService->calculateTreeRents($ownersTree);

        return $ownersTree;
    }

    /**
     * Get payments plots - executes the complex payment plots query
     * 
     * This method handles the most complex query (Query 3) from our analysis
     * 
     * @param int $year
     * @param int|null $contractId
     * @param int|null $annexId
     * @param int|null $ownerId
     * @param string|null $path
     * @param array $filterParams
     * @return Collection
     */
    protected function getPaymentsPlots(
        int $year,
        ?int $contractId = null,
        ?int $annexId = null,
        ?int $ownerId = null,
        ?string $path = null,
        array $filterParams = []
    ): Collection {
        $contractAnnexId = !empty($annexId) ? $annexId : $contractId;

        // Execute the complex payment plots query (Query 3)
        $plots = $this->paymentsRepository->getPaymentPlots(
            $year, 
            $contractAnnexId, 
            $ownerId, 
            $path, 
            $filterParams
        );

        // Get natural rent types (Query 4)
        $rentaNatArr = $this->paymentsRepository->getNatRents();

        // Get personal use data (Query 5)
        $personalUse = $this->paymentsRepository->getPersonalUseForOwners([
            'contract_id' => $contractAnnexId,
            'year' => $year,
            'chosen_years' => $year,
        ]);

        // Process all calculations for each plot
        return $this->calculationService->processPlotCalculations($plots, $rentaNatArr, $personalUse, $year);
    }
}
