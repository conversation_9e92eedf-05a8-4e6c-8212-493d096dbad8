<?php

namespace TF\Engine\Plugins\Core\Payments\Payments_Laravel\Services;

use Illuminate\Support\Collection;

/**
 * Data Formatting Service
 * 
 * Handles data formatting and presentation logic.
 * Converts the formattingOwnersData() and generateFooter() methods to Laravel patterns.
 */
class DataFormattingService
{
    /**
     * Format owners data for display
     * 
     * Converts the formattingOwnersData() method logic
     * 
     * @param array $ownersData
     * @return array
     */
    public function formatOwnersData(array $ownersData): array
    {
        $formattedData = [];

        foreach ($ownersData as $owner) {
            $owner = (array) $owner;
            
            $formattedOwner = [
                'id' => $owner['id'] ?? uniqid(),
                'uuid' => $owner['uuid'] ?? substr(md5(uniqid()), 0, 8),
                'owner_id' => $owner['owner_id'],
                'owner_names' => $owner['owner_names'],
                'contract_id' => $owner['contract_id'],
                'c_num' => $owner['c_num'],
                'egn_eik' => $owner['egn_eik'] ?? '',
                'address' => $owner['address'] ?? '',
                'phone' => $owner['phone'] ?? '',
                'mobile' => $owner['mobile'] ?? '',
                'iban' => $owner['iban'] ?? '',
                
                // Area data
                'all_owner_area' => $this->formatNumber($owner['all_owner_area'] ?? 0, 3),
                'owner_area' => $this->formatNumber($owner['owner_area'] ?? 0, 3),
                'pu_area' => $this->formatNumber($owner['pu_area'] ?? 0, 3),
                'cultivated_area' => $this->formatNumber($owner['cultivated_area'] ?? 0, 3),
                
                // Money data
                'renta' => $this->formatMoney($owner['renta'] ?? 0),
                'charged_renta' => $this->formatMoney($owner['charged_renta'] ?? 0),
                'paid_renta' => $this->formatMoney($owner['paid_renta'] ?? 0),
                'unpaid_renta' => $this->formatMoney($owner['unpaid_renta'] ?? 0),
                'overpaid_renta' => $this->formatMoney($owner['overpaid_renta'] ?? 0),
                'paid_via_money' => $this->formatMoney($owner['paid_via_money'] ?? 0),
                'paid_via_nat' => $this->formatMoney($owner['paid_via_nat'] ?? 0),
                
                // Natural rent data
                'renta_nat' => $this->formatNaturalRents($owner['renta_nat'] ?? []),
                'unpaid_renta_nat_arr' => $this->formatNaturalRentArray($owner['unpaid_renta_nat_arr'] ?? []),
                'overpaid_renta_nat_arr' => $this->formatNaturalRentArray($owner['overpaid_renta_nat_arr'] ?? []),
                'paid_renta_nat_sum' => $this->formatNaturalRentArray($owner['paid_renta_nat_sum'] ?? []),
                
                // Status flags
                'is_dead' => $owner['is_dead'] ?? false,
                'allow_owner_payment' => $owner['allow_owner_payment'] ?? true,
                'is_heritor' => $owner['is_heritor'] ?? false,
                
                // Contract data
                'contract_start_date' => $this->formatDate($owner['contract_start_date'] ?? null),
                'contract_due_date' => $this->formatDate($owner['contract_due_date'] ?? null),
                'farming_name' => $owner['farming_name'] ?? '',
                
                // Additional fields
                'path' => $owner['path'] ?? null,
                'owner_path_key' => $owner['owner_path_key'] ?? '',
                'rent_place_name' => $owner['rent_place_name'] ?? '',
            ];

            // Add personal use data if available
            if (isset($owner['personal_use'])) {
                $formattedOwner['personal_use'] = $this->formatPersonalUse($owner['personal_use']);
            }

            // Add representative data if available
            if (isset($owner['rep_names'])) {
                $formattedOwner['rep_names'] = $owner['rep_names'];
                $formattedOwner['rep_iban'] = $owner['rep_iban'] ?? '';
                $formattedOwner['rep_egn'] = $owner['rep_egn'] ?? '';
                $formattedOwner['rep_address'] = $owner['rep_address'] ?? '';
            }

            $formattedData[] = $formattedOwner;
        }

        return $formattedData;
    }

    /**
     * Generate footer data for totals
     * 
     * Converts the generateFooter() method logic
     * 
     * @param array $ownersData
     * @param array $excludeFields
     * @return array
     */
    public function generateFooter(array $ownersData, array $excludeFields = []): array
    {
        $footer = [
            'owner_names' => '<b>Общо</b>',
            'all_owner_area' => 0,
            'owner_area' => 0,
            'pu_area' => 0,
            'cultivated_area' => 0,
            'renta' => 0,
            'charged_renta' => 0,
            'paid_renta' => 0,
            'unpaid_renta' => 0,
            'overpaid_renta' => 0,
            'paid_via_money' => 0,
            'paid_via_nat' => 0,
        ];

        $naturalRentTotals = [];

        foreach ($ownersData as $owner) {
            $owner = (array) $owner;
            
            // Sum numerical fields
            $footer['all_owner_area'] += $owner['all_owner_area'] ?? 0;
            $footer['owner_area'] += $owner['owner_area'] ?? 0;
            $footer['pu_area'] += $owner['pu_area'] ?? 0;
            $footer['cultivated_area'] += $owner['cultivated_area'] ?? 0;
            $footer['renta'] += $owner['renta'] ?? 0;
            $footer['charged_renta'] += $owner['charged_renta'] ?? 0;
            $footer['paid_renta'] += $owner['paid_renta'] ?? 0;
            $footer['unpaid_renta'] += $owner['unpaid_renta'] ?? 0;
            $footer['overpaid_renta'] += $owner['overpaid_renta'] ?? 0;
            $footer['paid_via_money'] += $owner['paid_via_money'] ?? 0;
            $footer['paid_via_nat'] += $owner['paid_via_nat'] ?? 0;

            // Sum natural rent arrays
            if (isset($owner['unpaid_renta_nat_arr']) && is_array($owner['unpaid_renta_nat_arr'])) {
                foreach ($owner['unpaid_renta_nat_arr'] as $rentId => $amount) {
                    $naturalRentTotals['unpaid_renta_nat_arr'][$rentId] = 
                        ($naturalRentTotals['unpaid_renta_nat_arr'][$rentId] ?? 0) + $amount;
                }
            }

            if (isset($owner['overpaid_renta_nat_arr']) && is_array($owner['overpaid_renta_nat_arr'])) {
                foreach ($owner['overpaid_renta_nat_arr'] as $rentId => $amount) {
                    $naturalRentTotals['overpaid_renta_nat_arr'][$rentId] = 
                        ($naturalRentTotals['overpaid_renta_nat_arr'][$rentId] ?? 0) + $amount;
                }
            }
        }

        // Format the totals
        $footer['all_owner_area'] = $this->formatNumber($footer['all_owner_area'], 3);
        $footer['owner_area'] = $this->formatNumber($footer['owner_area'], 3);
        $footer['pu_area'] = $this->formatNumber($footer['pu_area'], 3);
        $footer['cultivated_area'] = $this->formatNumber($footer['cultivated_area'], 3);
        $footer['renta'] = $this->formatMoney($footer['renta']);
        $footer['charged_renta'] = $this->formatMoney($footer['charged_renta']);
        $footer['paid_renta'] = $this->formatMoney($footer['paid_renta']);
        $footer['unpaid_renta'] = $this->formatMoney($footer['unpaid_renta']);
        $footer['overpaid_renta'] = $this->formatMoney($footer['overpaid_renta']);
        $footer['paid_via_money'] = $this->formatMoney($footer['paid_via_money']);
        $footer['paid_via_nat'] = $this->formatMoney($footer['paid_via_nat']);

        // Add natural rent totals
        foreach ($naturalRentTotals as $field => $rentTotals) {
            $footer[$field] = [];
            foreach ($rentTotals as $rentId => $total) {
                $footer[$field][$rentId] = $this->formatNumber($total, 3);
            }
        }

        // Remove excluded fields
        foreach ($excludeFields as $field) {
            unset($footer[$field]);
        }

        return $footer;
    }

    /**
     * Format a number with specified precision
     * 
     * @param float $number
     * @param int $precision
     * @return string
     */
    protected function formatNumber(float $number, int $precision = 2): string
    {
        return number_format($number, $precision, '.', '');
    }

    /**
     * Format money amount
     * 
     * @param float $amount
     * @return string
     */
    protected function formatMoney(float $amount): string
    {
        return number_format($amount, 2, '.', '');
    }

    /**
     * Format date
     * 
     * @param string|null $date
     * @return string|null
     */
    protected function formatDate(?string $date): ?string
    {
        if (!$date) {
            return null;
        }

        try {
            return date('d.m.Y', strtotime($date));
        } catch (\Exception $e) {
            return $date;
        }
    }

    /**
     * Format natural rents array
     * 
     * @param array $naturalRents
     * @return array
     */
    protected function formatNaturalRents(array $naturalRents): array
    {
        $formatted = [];
        
        foreach ($naturalRents as $rentId => $amount) {
            $formatted[$rentId] = $this->formatNumber($amount, 3);
        }
        
        return $formatted;
    }

    /**
     * Format natural rent array
     * 
     * @param array $naturalRentArray
     * @return array
     */
    protected function formatNaturalRentArray(array $naturalRentArray): array
    {
        $formatted = [];
        
        foreach ($naturalRentArray as $rentId => $amount) {
            $formatted[$rentId] = $this->formatNumber($amount, 3);
        }
        
        return $formatted;
    }

    /**
     * Format personal use data
     * 
     * @param array $personalUse
     * @return array
     */
    protected function formatPersonalUse(array $personalUse): array
    {
        $formatted = [];
        
        foreach ($personalUse as $key => $value) {
            if (is_numeric($value)) {
                $formatted[$key] = $this->formatNumber($value, 3);
            } else {
                $formatted[$key] = $value;
            }
        }
        
        return $formatted;
    }

    /**
     * Format collection data for API response
     * 
     * @param Collection $collection
     * @return array
     */
    public function formatCollectionForResponse(Collection $collection): array
    {
        return $collection->map(function ($item) {
            return is_array($item) ? $item : (array) $item;
        })->toArray();
    }
}
