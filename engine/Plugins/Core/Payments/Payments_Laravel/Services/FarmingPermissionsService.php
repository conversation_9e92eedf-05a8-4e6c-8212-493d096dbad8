<?php

namespace TF\Engine\Plugins\Core\Payments\Payments_Laravel\Services;

use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Farming Permissions Service
 * 
 * Handles user farming permissions and access control.
 * Converts the getAllowedFarmingIds() logic to Laravel patterns.
 */
class FarmingPermissionsService
{
    /**
     * Get allowed farming IDs based on user permissions
     * 
     * This converts Query 1 from our analysis: the user farmings query
     * 
     * @return array
     * @throws Exception
     */
    public function getAllowedFarmingIds(): array
    {
        // Get user farmings using Laravel Query Builder (Query 1)
        $userFarmings = $this->getUserFarmings();

        // Validate that getUserFarmings() returns a valid array
        if (empty($userFarmings) || !is_array($userFarmings)) {
            // Return empty array as safe default when no farmings are available
            return [];
        }

        return array_keys($userFarmings);
    }

    /**
     * Get user farmings with detailed information
     * 
     * Converts the FarmingController->getUserFarmings() method to Laravel Query Builder
     * 
     * @param bool $detailed
     * @return array
     */
    public function getUserFarmings(bool $detailed = false): array
    {
        // In a real Laravel app, this would get the authenticated user
        // For now, we'll simulate the user data structure
        $user = $this->getCurrentUser();

        try {
            // Get user permission object IDs (this would be from a permissions system)
            $userFarmingIds = $this->getUserPermissionObjectIds($user);
            
            $query = DB::table('su_users_farming')
                ->whereIn('id', $userFarmingIds)
                ->where('group_id', $user['group_id'])
                ->orderBy('name', 'ASC');

        } catch (Exception $e) {
            // Fallback to group-based permissions
            $query = DB::table('su_users_farming')
                ->where('group_id', $user['group_id'])
                ->orderBy('name', 'ASC');
        }

        if ($detailed) {
            $query->select('*');
        } else {
            $query->select(['id', 'name']);
        }

        $farmingResults = $query->get();

        if ($farmingResults->isEmpty()) {
            return [];
        }

        $farmings = [];
        foreach ($farmingResults as $farming) {
            $farmings[$farming->id] = $detailed ? (array) $farming : $farming->name;
        }

        return $farmings;
    }

    /**
     * Get current user data
     * 
     * In a real Laravel app, this would use Auth::user()
     * 
     * @return array
     */
    protected function getCurrentUser(): array
    {
        // Simulate user data - in real Laravel app this would be:
        // return Auth::user()->toArray();
        
        return [
            'id' => 1,
            'group_id' => 1,
            'name' => 'Test User'
        ];
    }

    /**
     * Get user permission object IDs
     * 
     * This would integrate with a permissions system in a real Laravel app
     * 
     * @param array $user
     * @return array
     */
    protected function getUserPermissionObjectIds(array $user): array
    {
        // In a real Laravel app, this would query the permissions system
        // For now, return a default set of farming IDs
        
        return DB::table('su_users_farming')
            ->where('group_id', $user['group_id'])
            ->pluck('id')
            ->toArray();
    }

    /**
     * Get farming details by ID
     * 
     * @param int $farmingId
     * @return array|null
     */
    public function getFarmingById(int $farmingId): ?array
    {
        $farming = DB::table('su_users_farming')
            ->where('id', $farmingId)
            ->first();

        return $farming ? (array) $farming : null;
    }

    /**
     * Check if user has access to specific farming
     * 
     * @param int $farmingId
     * @return bool
     */
    public function hasAccessToFarming(int $farmingId): bool
    {
        $allowedIds = $this->getAllowedFarmingIds();
        return in_array($farmingId, $allowedIds);
    }

    /**
     * Get farmings with additional filtering
     * 
     * @param array $filters
     * @return Collection
     */
    public function getFarmingsWithFilters(array $filters = []): Collection
    {
        $query = DB::table('su_users_farming');

        if (isset($filters['group_id'])) {
            $query->where('group_id', $filters['group_id']);
        }

        if (isset($filters['active'])) {
            $query->where('active', $filters['active']);
        }

        if (isset($filters['name'])) {
            $query->where('name', 'ILIKE', '%' . $filters['name'] . '%');
        }

        return $query->orderBy('name', 'ASC')->get();
    }
}
