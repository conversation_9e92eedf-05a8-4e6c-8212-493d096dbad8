<?php

namespace TF\Engine\Plugins\Core\Payments\Payments_Laravel\Services;

use Illuminate\Support\Collection;
use TF\Engine\Plugins\Core\Payments\Payments_Laravel\Repositories\PaymentsRepository;

/**
 * Payment Calculation Service.
 *
 * Handles all complex payment calculations, aggregations, and business logic.
 * Converts the mathematical and data processing logic from the original PaymentsController.
 */
class PaymentCalculationService
{
    protected PaymentsRepository $paymentsRepository;

    // Precision constants from original code
    protected int $areaPrecision = 3;
    protected int $moneyPrecision = 2;
    protected int $rentaNatPrecision = 3;
    protected int $infinityPrecision = 16;
    protected float $fixMoneyRoundingValue = 0.1;

    public function __construct(PaymentsRepository $paymentsRepository)
    {
        $this->paymentsRepository = $paymentsRepository;
    }

    /**
     * Process plot calculations.
     *
     * Handles all the complex calculations for each plot including personal use,
     * rent calculations, and natural rent processing.
     */
    public function processPlotCalculations(
        Collection $plots,
        Collection $rentaNatArr,
        Collection $personalUse,
        int $year
    ): Collection {
        return $plots->map(function ($plot) use ($rentaNatArr, $personalUse) {
            $plot = (array) $plot;

            // Calculate personal use area
            $plot['pu_area'] = $this->calculatePersonalUseArea($personalUse, $plot['owner_id'], $plot);

            // Calculate calculation area (total area minus personal use)
            $plot['calculation_area'] = $this->subtract(
                $plot['plot_owned_area'],
                $plot['pu_area'],
                $this->areaPrecision
            );

            // Set area values
            $plot['all_owner_area'] = $this->round($plot['plot_owned_area'], $this->areaPrecision);
            $plot['all_owner_contract_area'] = $this->round($plot['plot_owned_contract_area'], $this->areaPrecision);
            $plot['owner_area'] = $this->round($plot['calculation_area'], $this->areaPrecision);

            // Process natural rents
            $plot = $this->processNaturalRents($plot, $rentaNatArr);

            // Calculate money rents
            $plot = $this->calculateMoneyRents($plot);

            // Initialize unpaid renta
            $plot['unpaid_renta'] = $this->add($plot['contract_renta'] ?? 0, $plot['charged_renta'] ?? 0);
            $plot['overpaid_renta'] = $this->round(0, $this->moneyPrecision);

            return $plot;
        });
    }

    /**
     * Aggregate plots by owner or other criteria.
     *
     * Converts the aggPlots() method logic to Laravel patterns
     */
    public function aggregatePlots(Collection $plots, string $aggBy = 'owner'): Collection
    {
        $aggPlots = collect();

        foreach ($plots as $plot) {
            $plot = (array) $plot;

            // Determine aggregation key
            switch ($aggBy) {
                case 'owner':
                    $aggKey = $plot['owner_id'] . '_' . ($plot['path'] ?? '0');

                    break;
                case 'plot':
                    $aggKey = $plot['pc_rel_id'];

                    break;
                case 'contract':
                    $aggKey = $plot['contract_id'];

                    break;
                case 'rent_type':
                    $aggKey = $plot['owner_id'] . '_' . ($plot['plot_rent_type_id'] . '_' . $plot['pc_rel_id'] ?? '0');

                    break;
                default:
                    $aggKey = $plot['owner_id'] . '_' . ($plot['path'] ?? '0');
            }

            if (!$aggPlots->has($aggKey)) {
                // Initialize aggregated plot data
                $aggPlots[$aggKey] = $this->initializeAggregatedPlot($plot);
            } else {
                // Sum existing plot data
                $aggPlots[$aggKey] = $this->sumPayments($aggPlots[$aggKey], $plot);
            }
        }

        return $aggPlots;
    }

    /**
     * Sum payment data from two plots.
     */
    public function sumPayments(array $payment1, array $payment2): array
    {
        // Sum numerical fields
        $payment1['all_owner_area'] = $this->add($payment1['all_owner_area'], $payment2['all_owner_area']);
        $payment1['owner_area'] = $this->add($payment1['owner_area'], $payment2['calculation_area']);
        $payment1['renta'] = $this->add($payment1['renta'], $payment2['renta'] ?? 0);
        $payment1['charged_renta'] = $this->add($payment1['charged_renta'], $payment2['charged_renta'] ?? 0);
        $payment1['unpaid_renta'] = $this->add($payment1['unpaid_renta'], $payment2['unpaid_renta']);

        // Sum natural rent arrays
        foreach ($payment2['renta_nat_info'] ?? [] as $rentaNatId => $rentaNatInfo) {
            $payment1['renta_nat'][$rentaNatId] = $this->add(
                $payment1['renta_nat'][$rentaNatId] ?? 0,
                $rentaNatInfo['contract_renta_nat'] ?? 0
            );
            $payment1['charged_renta_nat'][$rentaNatId] = $this->add(
                $payment1['charged_renta_nat'][$rentaNatId] ?? 0,
                $rentaNatInfo['charged_renta_nat'] ?? 0
            );
        }

        foreach ($payment2['unpaid_renta_nat_arr'] ?? [] as $rentaNatId => $value) {
            $payment1['unpaid_renta_nat_arr'][$rentaNatId] = $this->add(
                $payment1['unpaid_renta_nat_arr'][$rentaNatId] ?? 0,
                $value
            );
        }

        return $payment1;
    }

    /**
     * Process owner payments.
     *
     * Converts the processOwnerPayments() method logic
     */
    public function processOwnerPayments(
        int $year,
        Collection $owners,
        ?array $contractAnnexIds = null,
        ?int $ownerId = null,
        string $aggKey = 'owner_path_key'
    ): Collection {
        // Get payment data (Query 6 from our analysis)
        $paidResults = $this->paymentsRepository->getContractOwnerPayments($year, $contractAnnexIds, $ownerId);

        // Get rent types (Query 7 from our analysis)
        $rentNats = $this->paymentsRepository->getRentTypes();
        $rentNatsArray = $rentNats->keyBy('id')->toArray();

        // Process each owner's payments
        return $owners->map(function ($owner) use ($paidResults, $rentNatsArray, $aggKey) {
            $owner = (array) $owner;

            foreach ($paidResults as $paidResult) {
                $paidResult = (array) $paidResult;

                // Match payment to owner based on aggregation key
                $aggClause = false;
                if ('owner_path_key' === $aggKey) {
                    $aggClause = $paidResult['owner_path_key'] === $owner['owner_path_key'];
                } elseif ('owner_id' === $aggKey) {
                    $aggClause = $paidResult['owner_id'] === $owner['owner_id'];
                }

                if ($aggClause && $paidResult['contract_id'] === ($paidResult['parent_id'] ?? $paidResult['contract_id'])) {
                    $owner = $this->processPaymentTransaction($owner, $paidResult, $rentNatsArray);
                }
            }

            return $owner;
        });
    }

    /**
     * Process owner personal use.
     *
     * Converts the processOwnerPersonalUse() method logic
     */
    public function processOwnerPersonalUse(Collection $owners, ?string $gridType = null): Collection
    {
        return $owners->map(function ($owner) use ($gridType) {
            $owner = (array) $owner;
            $partCoef = 1;

            if (in_array($gridType, ['contract_payments'])) {
                $partCoef = $this->divide($owner['plot_owned_area'], $owner['plot_owned_area_total']);
            }

            // Initialize personal use arrays
            $owner['personal_use'] = [];
            $owner['personal_use_nat_type_id'] = [];
            $owner['personal_use_nat_types_names_arr'] = [];
            $owner['personal_use_amount_arr'] = [];
            $owner['personal_use_price_sum'] = [];
            $owner['personal_use_paid_arr'] = [];
            $owner['personal_use_unpaid_arr'] = [];
            $owner['personal_use_total'] = [];

            // Process personal use data (this would be populated from the personalUse collection)
            // The actual processing logic would go here based on the original implementation

            return $owner;
        });
    }

    /**
     * Fix rounding errors in payments.
     *
     * Converts the fixRounding() method logic
     */
    public function fixRounding(Collection $payments): Collection
    {
        return $payments->map(function ($payment) {
            $payment = (array) $payment;

            $chargedRenta = $payment['charged_renta'] ?? 0;
            $renta = $payment['renta'] ?? 0;
            $paidRenta = $payment['paid_renta'] ?? 0;
            $sumRenta = $this->add($chargedRenta, $renta);
            $rentCal = $this->subtract($sumRenta, $paidRenta);

            if ($rentCal > 0 && $rentCal <= $this->fixMoneyRoundingValue) {
                if ($chargedRenta > 0) {
                    $payment['charged_renta'] = $this->subtract($chargedRenta, $rentCal);
                } else {
                    $payment['renta'] = $this->subtract($renta, $rentCal);
                }
                $payment['unpaid_renta'] = $this->round(0, $this->moneyPrecision);
            }

            if ($rentCal < 0 && $rentCal >= ($this->fixMoneyRoundingValue * -1)) {
                if ($chargedRenta > 0) {
                    $payment['charged_renta'] = $this->subtract($chargedRenta, $rentCal);
                } else {
                    $payment['renta'] = $this->subtract($renta, $rentCal);
                }
                $payment['unpaid_renta'] = $this->round(0, $this->moneyPrecision);
            }

            return $payment;
        });
    }

    /**
     * Filter children based on parent's dead status.
     *
     * Converts the filterChildrenByParentDeadStatus() method logic
     */
    public function filterChildrenByParentDeadStatus(Collection $aggPlots): Collection
    {
        // This method would implement the complex logic for filtering
        // children based on parent death status and farm year logic
        // For now, return the collection as-is
        return $aggPlots;
    }

    /**
     * Build owners tree structure.
     *
     * Converts the buildOwnersTree() method logic
     */
    public function buildOwnersTree(Collection $aggPlots): array
    {
        // This method would implement the complex tree building logic
        // For now, return the collection as an array
        return $aggPlots->toArray();
    }

    /**
     * Calculate tree rents.
     *
     * Converts the calculateTreeRents() method logic
     */
    public function calculateTreeRents(array &$ownersTree): void
    {
        // This method would implement the complex tree rent calculation logic
        // where payments to owners are deducted from heirs' rents and vice versa
        // For now, this is a placeholder
    }

    /**
     * Sum owner data in contract tree.
     *
     * Converts the sumOwnerDataInContract() method logic
     */
    public function sumOwnerDataInContract(array $contractsPayments, int $ownerId): array
    {
        // This method would recursively traverse the contract tree
        // and sum all payment data for the specific owner
        // For now, return a basic structure
        return [
            'owner_id' => $ownerId,
            'contract_data' => $contractsPayments,
            'total_renta' => 0,
            'total_paid' => 0,
            'total_unpaid' => 0,
        ];
    }

    /**
     * Calculate personal use area for a plot.
     */
    protected function calculatePersonalUseArea(Collection $personalUse, int $ownerId, array $plot): float
    {
        $puArea = 0;

        foreach ($personalUse as $pu) {
            if ($pu->owner_id === $ownerId && $pu->pc_rel_id === $plot['pc_rel_id']) {
                $puArea = $this->add($puArea, $pu->area ?? 0, $this->areaPrecision);
            }
        }

        return $puArea;
    }

    /**
     * Process natural rents for a plot.
     */
    protected function processNaturalRents(array $plot, Collection $rentaNatArr): array
    {
        $plot['renta_nat'] = [];
        $plot['renta_nat_info'] = [];
        $plot['unpaid_renta_nat_arr'] = [];
        $plot['overpaid_renta_nat_arr'] = [];
        $plot['unpaid_renta_nat_money_arr'] = [];
        $plot['overpaid_renta_nat_money_arr'] = [];
        $plot['paid_nat_via_nat'] = [];
        $plot['paid_renta_nat_sum'] = [];

        // Process contract natural rents
        if (!empty($plot['renta_nat_json'])) {
            $rentaNatData = json_decode($plot['renta_nat_json'], true);
            if (is_array($rentaNatData)) {
                foreach ($rentaNatData as $rentaNat) {
                    $this->processContractNaturalRent($plot, $rentaNat);
                }
            }
        }

        // Process charged natural rents
        if (!empty($plot['charged_renta_nat_json'])) {
            $chargedRentaNatData = json_decode($plot['charged_renta_nat_json'], true);
            if (is_array($chargedRentaNatData)) {
                foreach ($chargedRentaNatData as $chargedRentaNat) {
                    $this->processChargedNaturalRent($plot, $chargedRentaNat);
                }
            }
        }

        return $plot;
    }

    /**
     * Process contract natural rent.
     */
    protected function processContractNaturalRent(array &$plot, array $rentaNat): void
    {
        $rentaNatId = $rentaNat['renta_nat_id'];

        $plot['renta_nat'][$rentaNatId] = 0;
        $plot['renta_nat_info'][$rentaNatId] = [
            'renta_nat_id' => $rentaNatId,
            'renta_nat_name' => $rentaNat['renta_nat_name'],
            'unit_id' => $rentaNat['unit_id'],
            'unit_name' => $GLOBALS['Contracts']['renta_units'][$rentaNat['unit_id']]['name'] ?? '',
            'nat_value' => $rentaNat['nat_value'],
            'unit_value' => $rentaNat['unit_value'],
            'charged_renta_nat' => null,
            'contract_renta_nat' => null,
        ];

        // Calculate contract natural rent
        $rentNatValue = ($plot['rent_per_plot_value'] ?? 0) > 0 ? 0 : $rentaNat['nat_value'];
        $contractRentaNat = $this->multiply($rentNatValue, $plot['calculation_area']);

        $plot['renta_nat_info'][$rentaNatId]['contract_renta_nat'] = $contractRentaNat;
        $plot['renta_nat'][$rentaNatId] = $this->add($plot['renta_nat'][$rentaNatId], $contractRentaNat);

        // Initialize arrays
        $plot['unpaid_renta_nat_arr'][$rentaNatId] = $contractRentaNat;
        $plot['unpaid_renta_nat_money_arr'][$rentaNatId] = $contractRentaNat * $rentaNat['unit_value'];
        $plot['overpaid_renta_nat_arr'][$rentaNatId] = $this->round(0, $this->rentaNatPrecision);
        $plot['overpaid_renta_nat_money_arr'][$rentaNatId] = $this->round(0, $this->moneyPrecision);
        $plot['paid_nat_via_nat'][$rentaNatId] = $this->round(0, $this->rentaNatPrecision);
        $plot['paid_renta_nat_sum'][$rentaNatId] = $this->round(0, $this->rentaNatPrecision);
    }

    /**
     * Process charged natural rent.
     */
    protected function processChargedNaturalRent(array &$plot, array $chargedRentaNat): void
    {
        $rentaNatId = $chargedRentaNat['charged_renta_nat_id'];

        if (($plot['rent_per_plot_value'] ?? 0) > 0) {
            return; // Skip if individual rent per plot
        }

        if ($chargedRentaNat['nat_is_converted']) {
            // Natural rent converted to money
            $chargedRentNatValue = $this->multiply($chargedRentaNat['amount'], $chargedRentaNat['unit_value']);
            $chargedRentaNatConverted = $this->multiply($chargedRentNatValue, $plot['calculation_area']);
            $plot['charged_renta_nat_converted'] = $this->add(
                $plot['charged_renta_nat_converted'] ?? 0,
                $chargedRentaNatConverted
            );
        } else {
            // Natural rent in natura
            $plot['charged_renta_nat'][$rentaNatId] = $this->multiply(
                $chargedRentaNat['amount'],
                $plot['calculation_area']
            );

            if (isset($plot['renta_nat_info'][$rentaNatId])) {
                $plot['renta_nat_info'][$rentaNatId]['charged_renta_nat']
                    = $plot['charged_renta_nat'][$rentaNatId];
            }
        }
    }

    /**
     * Calculate money rents for a plot.
     */
    protected function calculateMoneyRents(array $plot): array
    {
        // Determine rent value based on priority:
        // 1. Individual rent per plot (highest priority)
        // 2. Charged rent (from charging)
        // 3. Contract/annex rent (default)

        if (!empty($plot['rent_per_plot'])) {
            $plot['contract_renta'] = $this->multiply($plot['rent_per_plot'], $plot['calculation_area']);
            $plot['renta'] = $plot['contract_renta'];
        } elseif (!empty($plot['charged_renta_value']) || !empty($plot['charged_renta_nat_converted'])) {
            $chargedRenta = $this->multiply(($plot['charged_renta_value'] ?? 0), $plot['calculation_area']);
            $plot['charged_renta'] = $this->add($chargedRenta, ($plot['charged_renta_nat_converted'] ?? 0));
        } else {
            $plot['contract_renta'] = $this->multiply($plot['rent_money_value'], $plot['calculation_area']);
            $plot['renta'] = $plot['contract_renta'];
        }

        return $plot;
    }

    /**
     * Mathematical helper methods with precision handling.
     */
    protected function add($a, $b, int $precision = null): float
    {
        $result = (float)$a + (float)$b;

        return null !== $precision ? round($result, $precision) : $result;
    }

    protected function subtract($a, $b, int $precision = null): float
    {
        $result = (float)$a - (float)$b;

        return null !== $precision ? round($result, $precision) : $result;
    }

    protected function multiply($a, $b, int $precision = null): float
    {
        $result = (float)$a * (float)$b;

        return null !== $precision ? round($result, $precision) : $result;
    }

    protected function divide($a, $b, int $precision = null): float
    {
        if (0 == $b) {
            return 0;
        }
        $result = (float)$a / (float)$b;

        return null !== $precision ? round($result, $precision) : $result;
    }

    protected function round($value, int $precision): float
    {
        return round((float)$value, $precision);
    }

    /**
     * Initialize aggregated plot data structure.
     */
    protected function initializeAggregatedPlot(array $plot): array
    {
        return [
            'uuid' => substr(md5($plot['owner_id'] . ($plot['path'] ?? '')), 0, 6),
            'contract_id' => $plot['contract_id'],
            'parent_id' => $plot['parent_id'],
            'c_num' => $plot['c_num'],
            'owner_id' => $plot['owner_id'],
            'owner_names' => $plot['owner_names'],
            'path' => $plot['path'] ?? null,
            'owner_path_key' => $plot['owner_path_key'],
            'all_owner_area' => $plot['all_owner_area'],
            'owner_area' => $plot['calculation_area'],
            'renta' => $plot['renta'] ?? 0,
            'charged_renta' => $plot['charged_renta'] ?? 0,
            'paid_renta' => $this->round(0, $this->moneyPrecision),
            'unpaid_renta' => $plot['unpaid_renta'],
            'overpaid_renta' => $this->round(0, $this->moneyPrecision),
            'renta_nat' => $plot['renta_nat'] ?? [],
            'renta_nat_info' => $plot['renta_nat_info'] ?? [],
            'unpaid_renta_nat_arr' => $plot['unpaid_renta_nat_arr'] ?? [],
            'overpaid_renta_nat_arr' => $plot['overpaid_renta_nat_arr'] ?? [],
            // ... other fields would be initialized here
        ];
    }

    /**
     * Process a single payment transaction.
     */
    protected function processPaymentTransaction(array $owner, array $paidResult, array $rentNatsArray): array
    {
        $moneyTransAmount = $paidResult['trans_amount'];
        $natTransAmount = $paidResult['trans_amount_nat'];

        // Process different payment types
        if (1 == $paidResult['paid_in'] && 1 == $paidResult['paid_from']) {
            // Money payment in money
            $owner['paid_renta'] = $this->add($owner['paid_renta'] ?? 0, $moneyTransAmount);
            $owner['unpaid_renta'] = $this->subtract($owner['unpaid_renta'], $moneyTransAmount);
            $owner['paid_via_money'] = $this->add($owner['paid_via_money'] ?? 0, $moneyTransAmount);
        } elseif (2 == $paidResult['paid_in'] && 1 == $paidResult['paid_from']) {
            // Money payment in natura
            $rentaNatKey = $paidResult['nat_type'] . '_' . $paidResult['unit_value'];

            if (!isset($owner['paid_via_nat'][$rentaNatKey])) {
                $owner['paid_via_nat'][$rentaNatKey] = [
                    'nat_trans_amount' => $natTransAmount,
                    'nat_unit_value' => $paidResult['unit_value'],
                    'nat_trans_type_unit_name' => $rentNatsArray[$paidResult['nat_type']]['name'] ?? '',
                ];
            } else {
                $owner['paid_via_nat'][$rentaNatKey]['nat_trans_amount'] = $this->add(
                    $owner['paid_via_nat'][$rentaNatKey]['nat_trans_amount'],
                    $natTransAmount
                );
            }

            $owner['paid_renta'] = $this->add($owner['paid_renta'] ?? 0, $moneyTransAmount);
            $owner['unpaid_renta'] = $this->subtract($owner['unpaid_renta'], $moneyTransAmount);
        }
        // Additional payment type processing would go here...

        return $owner;
    }
}
