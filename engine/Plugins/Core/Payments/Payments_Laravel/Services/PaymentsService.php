<?php

namespace TF\Engine\Plugins\Core\Payments\Payments_Laravel\Services;

use Exception;
use Illuminate\Support\Collection;
use TF\Engine\Plugins\Core\Payments\Payments_Laravel\Repositories\PaymentsRepository;

/**
 * Main Payments Service.
 *
 * Handles the broader payments functionality including payroll and contract payments.
 * Converts the remaining PaymentsController methods to Laravel patterns.
 */
class PaymentsService
{
    protected PaymentsRepository $paymentsRepository;
    protected FarmingPermissionsService $farmingPermissionsService;
    protected PaymentCalculationService $calculationService;
    protected DataFormattingService $formattingService;
    protected OwnerPaymentsService $ownerPaymentsService;

    public function __construct(
        PaymentsRepository $paymentsRepository,
        FarmingPermissionsService $farmingPermissionsService,
        PaymentCalculationService $calculationService,
        DataFormattingService $formattingService,
        OwnerPaymentsService $ownerPaymentsService
    ) {
        $this->paymentsRepository = $paymentsRepository;
        $this->farmingPermissionsService = $farmingPermissionsService;
        $this->calculationService = $calculationService;
        $this->formattingService = $formattingService;
        $this->ownerPaymentsService = $ownerPaymentsService;
    }

    /**
     * Get owners payroll.
     *
     * Converts the original getOwnersPayroll() method
     *
     * @throws Exception
     */
    public function getOwnersPayroll(
        int $year,
        array $filterParams = [],
        ?int $rows = null,
        ?int $page = null,
        bool $returnFlatOwners = false
    ): array {
        $result = [];

        // Add filter by allowed farmings
        $allowedFarmingIds = $this->farmingPermissionsService->getAllowedFarmingIds();

        // Handle case when no allowed farming IDs are available
        if (empty($allowedFarmingIds)) {
            throw new Exception('User has no allowed farmings');
        }

        $filterParams['payroll_farming'] = !empty($filterParams['payroll_farming']) && '' != $filterParams['payroll_farming'][0]
            ? array_intersect($filterParams['payroll_farming'], $allowedFarmingIds)
            : $allowedFarmingIds;

        // Get all contracts based on filter parameters
        $ownerContracts = $this->paymentsRepository->getOwnerContracts($year, $filterParams);

        // 1. Collect all owner_ids in array - needed for pagination
        $allOwnerIdsString = implode(',', array_column($ownerContracts->toArray(), 'owner_ids'));
        $allOwnerIdsArray = array_unique(explode(',', $allOwnerIdsString));

        // Get only owners for the desired page
        if ($page && $rows) {
            $start = ($page - 1) * $rows;
            $limitedOwners = array_slice($allOwnerIdsArray, $start, $rows);

            // Filter contracts that contain owners from the desired page
            $filteredContracts = $ownerContracts->filter(function ($item) use ($limitedOwners) {
                $itemOwnerIds = explode(',', $item->owner_ids);

                return count(array_intersect($itemOwnerIds, $limitedOwners)) > 0;
            });
        } else {
            $filteredContracts = $ownerContracts;
        }

        $flatOwners = [];

        // Process each contract to calculate payments
        foreach ($filteredContracts as $contract) {
            $contract = (array) $contract;
            $contractId = $contract['contract_id'];
            $contract['parent_ids'] = explode(',', $contract['parent_ids'] ?? '') ?: null;
            $contract['owner_ids'] = explode(',', $contract['owner_ids'] ?? '') ?: null;
            $annexId = null;

            if ($contract['parent_id']) {
                $contractId = $contract['parent_id'];
                $annexId = $contract['contract_id'];
            }

            // Build owner tree for the contract with all calculations
            $contractsPayments = $this->ownerPaymentsService->getPayments($year, $contractId, $annexId, null, $filterParams);

            // Filter owners and heirs by owner_ids of the contract
            $filterParams['owner_ids'] = $contract['owner_ids'];
            $filteredOwners = $this->filterOwnersTree($contractsPayments, $filterParams);

            // Transform calculated data for owners into a two-dimensional array
            $flatOwners[$contractId] = $this->flattenOwnersTree($filteredOwners);
        }

        // Return flat owners if requested
        if ($returnFlatOwners) {
            return [
                'rows' => $this->formattingService->formatOwnersData($flatOwners),
                'total' => count($flatOwners),
                'footer' => [],
            ];
        }

        // Sum data for all owners and their heirs
        foreach ($flatOwners as $contractId => $contractOwners) {
            foreach ($contractOwners as $ownerPath => $owner) {
                $result[$ownerPath] = $this->calculationService->calculateOwnerData($result[$ownerPath] ?? [], $owner);
            }
        }

        // Build owners tree again for display in payroll
        $ownersTree = $this->calculationService->buildOwnersTree(collect($result));

        return [
            'rows' => $this->formattingService->formatOwnersData($ownersTree),
            'total' => count($allOwnerIdsArray),
            'footer' => [$this->formattingService->generateFooter($ownersTree, ['timespan'])],
        ];
    }

    /**
     * Get owner payroll for a specific owner.
     *
     * Converts the original getOwnerPayroll() method
     */
    public function getOwnerPayroll(int $year, ?int $ownerId = null, ?string $path = null, array $filterParams = []): array
    {
        // Get all plots for the contract with rents
        $plots = $this->getPaymentsPlots($year, null, null, $ownerId, $path, $filterParams);

        // Process owner personal use
        $plots = $this->calculationService->processOwnerPersonalUse($plots);

        $aggPlots = $this->calculationService->aggregatePlots($plots, 'rent_type');
        $aggPlotsByContract = $this->calculationService->aggregatePlots($plots, 'contract');

        if (!isset($filterParams['no_contract_payments'])) {
            // Process payments for owners by contracts and calculate remaining rents
            $aggPlots = $this->contractsPaymentsMapping($aggPlots, $aggPlotsByContract, $year, $ownerId, $path);
        }

        // Format data for grid display
        return $this->formattingService->formatOwnersData($aggPlots->toArray());
    }

    /**
     * Get contract payments.
     *
     * Converts the original getContractPayments() method
     */
    public function getContractPayments(
        int $year,
        ?int $contractId = null,
        ?int $annexId = null,
        ?int $page = null,
        ?int $rows = null,
        ?string $sort = null,
        ?string $order = null
    ): array {
        $payments = $this->ownerPaymentsService->getPayments($year, $contractId, $annexId, 'contract_payments');

        return [
            'rows' => $this->formattingService->formatOwnersData($payments),
            'total' => count($payments),
            'footer' => [
                $this->formattingService->generateFooter($payments),
            ],
        ];
    }

    /**
     * Calculate owner data aggregation.
     *
     * This method would be implemented in PaymentCalculationService
     * but is called from here for backwards compatibility
     */
    public function calculateOwnerData(array $owner1, array $owner2): array
    {
        return $this->calculationService->sumPayments($owner1, $owner2);
    }

    /**
     * Get payment statistics.
     */
    public function getPaymentStatistics(int $year, array $filterParams = []): array
    {
        $allowedFarmingIds = $this->farmingPermissionsService->getAllowedFarmingIds();

        if (empty($allowedFarmingIds)) {
            return [
                'total_contracts' => 0,
                'total_owners' => 0,
                'total_renta' => 0,
                'total_paid' => 0,
                'total_unpaid' => 0,
            ];
        }

        $filterParams['payroll_farming'] = $allowedFarmingIds;
        $ownerContracts = $this->paymentsRepository->getOwnerContracts($year, $filterParams);

        $stats = [
            'total_contracts' => $ownerContracts->count(),
            'total_owners' => 0,
            'total_renta' => 0,
            'total_paid' => 0,
            'total_unpaid' => 0,
        ];

        // Calculate statistics from contracts
        $allOwnerIds = [];
        foreach ($ownerContracts as $contract) {
            $contract = (array) $contract;
            $ownerIds = explode(',', $contract['owner_ids'] ?? '');
            $allOwnerIds = array_merge($allOwnerIds, $ownerIds);
        }

        $stats['total_owners'] = count(array_unique($allOwnerIds));

        return $stats;
    }

    /**
     * Export payments data.
     */
    public function exportPaymentsData(int $year, array $filterParams = [], string $format = 'array'): array
    {
        $payrollData = $this->getOwnersPayroll($year, $filterParams);

        switch ($format) {
            case 'csv':
                return $this->formatForCsv($payrollData['rows']);
            case 'excel':
                return $this->formatForExcel($payrollData['rows']);
            default:
                return $payrollData;
        }
    }

    /**
     * Get payments plots.
     */
    protected function getPaymentsPlots(
        int $year,
        ?int $contractId = null,
        ?int $annexId = null,
        ?int $ownerId = null,
        ?string $path = null,
        array $filterParams = []
    ): Collection {
        $contractAnnexId = !empty($annexId) ? $annexId : $contractId;
        $plots = $this->paymentsRepository->getPaymentPlots($year, $contractAnnexId, $ownerId, $path, $filterParams);

        $rentaNatArr = $this->paymentsRepository->getNatRents();

        // Get personal use for specific criteria
        $personalUse = $this->paymentsRepository->getPersonalUseForOwners([
            'contract_id' => $contractAnnexId,
            'year' => $year,
            'chosen_years' => $year,
        ]);

        // Process all calculations for plots
        return $this->calculationService->processPlotCalculations($plots, $rentaNatArr, $personalUse, $year);
    }

    /**
     * Map contracts payments.
     *
     * Converts the contractsPaymentsMapping() method logic
     */
    protected function contractsPaymentsMapping(
        Collection $aggPlots,
        Collection $aggPlotsByContract,
        int $year,
        ?int $ownerId = null,
        ?string $path = null
    ): Collection {
        foreach ($aggPlotsByContract as $contract) {
            $contract = (array) $contract;
            $contractId = $contract['contract_id'];
            $annexId = null;
            $ownerPayment = null;

            if ($contract['parent_id']) {
                $contractId = $contract['parent_id'];
                $annexId = $contract['contract_id'];
            }

            $contractPayments = $this->ownerPaymentsService->getPayments($year, $contractId, $annexId);
            if ($contractPayments) {
                $ownerPayment = $this->getOwnerDataFromContract($contractPayments, $path ?? $ownerId);
            }

            // Map payment data to aggregated plots
            $aggPlots = $aggPlots->map(function ($plot) use ($contract, $ownerPayment) {
                $plot = (array) $plot;
                if ($plot['contract_id'] == $contract['contract_id'] && $plot['parent_id'] == $contract['parent_id']) {
                    $plot['paid_renta'] = $ownerPayment['paid_renta'] ?? 0;
                    $plot['unpaid_renta'] = $ownerPayment['unpaid_renta'] ?? 0;
                    $plot['overpaid_renta'] = $ownerPayment['overpaid_renta'] ?? 0;
                    // ... map other payment fields
                }

                return $plot;
            });
        }

        return $aggPlots;
    }

    /**
     * Filter owners tree based on criteria.
     */
    protected function filterOwnersTree(array $contractsPayments, array $filterParams): array
    {
        // This method would implement the complex filtering logic
        // for owners and heirs based on various criteria
        // For now, return the data as-is
        return $contractsPayments;
    }

    /**
     * Flatten owners tree structure.
     */
    protected function flattenOwnersTree(array $filteredOwners): array
    {
        // This method would implement the tree flattening logic
        // converting the hierarchical structure to a flat array
        // For now, return the data as-is
        return $filteredOwners;
    }

    /**
     * Get owner data from contract.
     *
     * @param int|string $ownerIdOrPath
     */
    protected function getOwnerDataFromContract(array $contractPayments, $ownerIdOrPath): array
    {
        // This method would extract specific owner data from the contract payments tree
        // For now, return a basic structure
        return [
            'paid_renta' => 0,
            'unpaid_renta' => 0,
            'overpaid_renta' => 0,
            'paid_renta_by' => '',
            'paid_nat_via_money' => 0,
            'paid_nat_via_nat' => 0,
            'paid_renta_nat_sum' => [],
            'overpaid_renta_nat_money_arr' => [],
            'unpaid_renta_nat_money_arr' => [],
            'overpaid_renta_nat_arr' => [],
            'unpaid_renta_nat_arr' => [],
            'paid_via_nat' => [],
            'paid_via_money' => 0,
            'rent_place_name' => '',
        ];
    }

    /**
     * Format data for CSV export.
     */
    protected function formatForCsv(array $data): array
    {
        // Implementation for CSV formatting
        return $data;
    }

    /**
     * Format data for Excel export.
     */
    protected function formatForExcel(array $data): array
    {
        // Implementation for Excel formatting
        return $data;
    }
}
