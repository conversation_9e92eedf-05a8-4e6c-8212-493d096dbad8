# Laravel Payments Implementation

This directory contains a complete Laravel-style implementation of the Payments functionality, converted from the original PHP PaymentsController.

## Overview

The Laravel implementation maintains all the complex financial calculations and data relationships while using modern Laravel patterns and Query Builder syntax. It preserves the original functionality including:

- Complex SQL queries with CTEs and JSON aggregations
- Mathematical calculations with precision handling
- Owner hierarchy and tree structure processing
- Natural rent calculations and conversions
- Payment processing and aggregation
- Personal use calculations
- Rounding error corrections

## Architecture

The implementation follows Laravel best practices with a clear separation of concerns:

### Controllers
- **PaymentsController.php** - Laravel-style controller with dependency injection and proper response handling

### Services
- **OwnerPaymentsService.php** - Main business logic service handling the core `getOwnerPayments()` functionality
- **FarmingPermissionsService.php** - Handles user farming permissions and access control
- **PaymentCalculationService.php** - Complex payment calculations, aggregations, and mathematical operations
- **DataFormattingService.php** - Data formatting and presentation logic
- **PaymentsService.php** - Broader payments functionality including payroll and contract payments

### Repositories
- **PaymentsRepository.php** - Database layer with Laravel Query Builder implementations of all 8 SQL queries

### Models (Eloquent)
- **Owner.php** - Owner model with relationships and business logic
- **Contract.php** - Contract model with relationships and date calculations
- **Payment.php** - Payment model with transaction logic and relationships

## SQL Query Conversion

The implementation converts all 8 identified SQL queries from the original analysis:

1. **User farmings** - `FarmingPermissionsService::getUserFarmings()`
2. **Owner contracts** - `PaymentsRepository::getOwnerContracts()` (with CTE conversion)
3. **Payment plots** - `PaymentsRepository::getPaymentPlots()` (complex query with JSON aggregations)
4. **Natural rent types** - `PaymentsRepository::getNatRents()`
5. **Personal use data** - `PaymentsRepository::getPersonalUseForOwners()`
6. **Payment data** - `PaymentsRepository::getContractOwnerPayments()`
7. **Rent types lookup** - `PaymentsRepository::getRentTypes()`
8. **Farming details** - `PaymentsRepository::getFarmingDetails()`

## Key Features

### Laravel Query Builder
All raw SQL queries have been converted to Laravel Query Builder syntax while maintaining the same functionality:

```php
// Example: Complex CTE query conversion
DB::table('su_plots_owners_rel as spor')
    ->select([
        'c.id as contract_id',
        DB::raw('string_agg(DISTINCT c.parent_id::text, \',\') as parent_id'),
        DB::raw('string_agg(DISTINCT o.id::text, \',\') as owner_ids')
    ])
    ->leftJoin('su_contracts_plots_rel as scpr', function ($join) {
        $join->on('scpr.id', '=', 'spor.pc_rel_id')
             ->where('scpr.annex_action', '=', 'added');
    })
    // ... additional joins and conditions
```

### Precision Mathematics
All mathematical operations maintain the original precision requirements:

```php
protected function add($a, $b, int $precision = null): float
{
    $result = (float)$a + (float)$b;
    return $precision !== null ? round($result, $precision) : $result;
}
```

### Complex Business Logic
The implementation preserves all complex business logic including:
- Owner hierarchy processing
- Natural rent calculations
- Personal use area calculations
- Payment aggregation and tree building
- Rounding error corrections

## Usage

### Basic Usage
```php
// Get owner payments
$ownerPaymentsService = new OwnerPaymentsService($paymentsRepository, $calculationService);
$payments = $ownerPaymentsService->getOwnerPayments($year, $contractId, $annexId, $ownerId);

// Get owners payroll
$paymentsService = new PaymentsService(/* dependencies */);
$payroll = $paymentsService->getOwnersPayroll($year, $filterParams);
```

### Controller Usage
```php
// In a Laravel controller
public function getOwnerPayments(Request $request): JsonResponse
{
    $payments = $this->ownerPaymentsService->getOwnerPayments(
        $request->input('year'),
        $request->input('contract_id'),
        $request->input('annex_id'),
        $request->input('owner_id')
    );
    
    return response()->json($payments);
}
```

## Data Flow

1. **Permission Check** - `FarmingPermissionsService` validates user access
2. **Contract Retrieval** - `PaymentsRepository` gets owner contracts
3. **Plot Data** - Complex query retrieves plot, owner, and rent data
4. **Calculations** - `PaymentCalculationService` processes all mathematical operations
5. **Payment Processing** - Payment transactions are applied to calculated rents
6. **Tree Building** - Owner hierarchy is constructed and calculated
7. **Formatting** - `DataFormattingService` formats data for presentation

## Database Tables

The implementation works with the following database tables:
- `su_users_farming` - User farming permissions
- `su_contracts` - Contracts and annexes
- `su_owners` - Owner information
- `su_plots_owners_rel` - Plot-owner relationships
- `su_contracts_plots_rel` - Contract-plot relationships
- `su_payments` - Payment transactions
- `su_renta_nat` - Natural rent types
- `layer_kvs` - Plot geographic data

## Compatibility

This Laravel implementation is functionally equivalent to the original PHP code and maintains:
- All SQL query results
- All mathematical calculations
- All business logic rules
- All data relationships
- All precision requirements

## Future Enhancements

The Laravel structure allows for easy extension with:
- Caching layers
- Event-driven architecture
- API rate limiting
- Advanced validation
- Background job processing
- Real-time notifications
- Advanced reporting features

## Testing

The service-based architecture makes the code highly testable:
- Unit tests for calculation services
- Integration tests for repository queries
- Feature tests for controller endpoints
- Mock-based testing for external dependencies
